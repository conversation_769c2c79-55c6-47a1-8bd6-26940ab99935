import { CheckCircle, XCircle, Clock, Home, MapPin, DollarSign, Phone, Mail } from "lucide-react";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PropertyRequest } from "@/hooks/usePropertyRequests";
import { useAuth } from "@/contexts/AuthContext";
import { useTranslation } from "@/contexts/TranslationContext";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";

interface PropertyRequestCardProps {
  request: PropertyRequest;
  onUpdateStatus: (id: string, status: PropertyRequest['status']) => void;
}

export function PropertyRequestCard({ request, onUpdateStatus }: PropertyRequestCardProps) {
  const { isAdmin } = useAuth();
  const { t } = useTranslation();
  const { formatPrice } = useExchangeRate();

  // Function to render status badge
  const renderStatusBadge = (status: PropertyRequest['status']) => {
    switch (status) {
      case 'open':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Clock className="mr-1 h-3 w-3" /> Open
          </Badge>
        );
      case 'in-progress':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="mr-1 h-3 w-3" /> In Progress
          </Badge>
        );
      case 'matched':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" /> Matched
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <XCircle className="mr-1 h-3 w-3" /> Closed
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Function to render contact method icon
  const renderContactIcon = (method: string) => {
    if (method.toLowerCase().includes('phone') || method.toLowerCase().includes('call')) {
      return <Phone className="h-4 w-4 mr-1" />;
    } else if (method.toLowerCase().includes('email') || method.toLowerCase().includes('mail')) {
      return <Mail className="h-4 w-4 mr-1" />;
    } else {
      return null;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-4 pb-0">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold capitalize">{request.property_type}</h3>
            <div className="mt-1 flex items-center text-sm text-muted-foreground">
              <MapPin className="mr-1 h-3 w-3" /> {request.location}
            </div>
          </div>
          <div>{renderStatusBadge(request.status)}</div>
        </div>
      </CardHeader>

      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center text-sm">
            <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
            <span>
              {formatPrice(request.min_price)} - {formatPrice(request.max_price)}
            </span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-1">
              <Home className="h-4 w-4" />
              <span>{request.min_bedrooms}+ beds</span>
            </div>
            <div className="flex items-center gap-1">
              <span>{request.min_bathrooms}+ baths</span>
            </div>
            <div className="flex items-center gap-1">
              <span>{request.min_size}+ m²</span>
            </div>
          </div>

          {request.details && (
            <div className="mt-2 text-sm text-muted-foreground">
              <p className="line-clamp-2">{request.details}</p>
            </div>
          )}

          <div className="flex items-center text-sm mt-2">
            <div className="flex items-center text-muted-foreground">
              {renderContactIcon(request.contact_preference)}
              <span>Contact: {request.contact_preference}</span>
            </div>
          </div>

          <div className="text-xs text-muted-foreground mt-2">
            Requested on: {formatDate(request.created_at)}
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex justify-end">
        {isAdmin() && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                Update Status
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onUpdateStatus(request.id, 'open')}>
                Mark as Open
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onUpdateStatus(request.id, 'in-progress')}>
                Mark as In Progress
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onUpdateStatus(request.id, 'matched')}>
                Mark as Matched
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onUpdateStatus(request.id, 'closed')}>
                Mark as Closed
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </CardFooter>
    </Card>
  );
}
