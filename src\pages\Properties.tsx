
import { useState, useCallback } from "react";
import { Link } from "react-router-dom";
import { Building, Plus } from "lucide-react";
import { Layout } from "@/components/layout/Layout";
import { PropertyTable } from "@/components/properties/PropertyTable";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { PropertyCard } from "@/components/properties/PropertyCard";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useProperties, useDeleteProperty } from "@/hooks/useProperties";
import { useTranslation } from "@/contexts/TranslationContext";

const Properties = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { data: properties = [], isLoading, error } = useProperties();
  const deletePropertyMutation = useDeleteProperty();
  const { t } = useTranslation();

  const handleDeleteProperty = useCallback((id: string) => {
    deletePropertyMutation.mutate(id, {
      onSuccess: () => {
        toast({
          title: t('properties.deleted'),
          description: t('properties.deletedSuccess'),
        });
      },
      onError: (error) => {
        toast({
          title: t('common.error'),
          description: `${t('properties.deleteError')}: ${error.message}`,
          variant: "destructive",
        });
      }
    });
  }, [deletePropertyMutation, toast]);

  return (
    <Layout>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('properties.title')}</h1>
            <p className="text-muted-foreground">
              {t('properties.managePortfolio')}
            </p>
          </div>

          {user && (
            <Button asChild>
              <Link to="/properties/add">
                <Plus className="mr-2 h-4 w-4" /> {t('properties.addProperty')}
              </Link>
            </Button>
          )}
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Building className="h-5 w-5" />
              <div>
                <CardTitle>{t('properties.propertyListings')}</CardTitle>
                <CardDescription>{t('properties.viewAndManage')}</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex h-40 items-center justify-center">
                <p className="text-muted-foreground">{t('common.loading')}</p>
              </div>
            ) : error ? (
              <div className="flex h-40 items-center justify-center">
                <p className="text-red-500">{t('properties.loadError')}: {(error as Error).message}</p>
              </div>
            ) : (
              <Tabs defaultValue="table">
                <div className="flex justify-between items-center mb-4">
                  <TabsList>
                    <TabsTrigger value="table">{t('properties.tableView')}</TabsTrigger>
                    <TabsTrigger value="grid">{t('properties.gridView')}</TabsTrigger>
                  </TabsList>
                  <div className="text-sm text-muted-foreground">
                    {t('properties.total')}: {properties.length} {t('properties.title')}
                  </div>
                </div>

                <TabsContent value="table" className="mt-0">
                  <PropertyTable
                    properties={properties}
                    onDelete={handleDeleteProperty}
                  />
                </TabsContent>

                <TabsContent value="grid" className="mt-0">
                  <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {properties.map(property => (
                      <PropertyCard key={property.id} property={property} />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Properties;
