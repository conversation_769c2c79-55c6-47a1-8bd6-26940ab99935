
import { ReactNode, useEffect, useState } from "react";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { user, loading, session } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isChecking, setIsChecking] = useState(true);
  const [hasTokenError, setHasTokenError] = useState(false);

  useEffect(() => {
    // Check for token errors in console logs
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('Invalid Refresh Token')) {
        setHasTokenError(true);
        // Clear any stored tokens
        localStorage.removeItem('supabase.auth.token');

        toast({
          title: "Session expired",
          description: "Your session has expired. Please sign in again.",
          variant: "destructive",
        });

        // Redirect to login after a short delay
        setTimeout(() => {
          navigate('/auth/login', { state: { from: location.pathname }, replace: true });
        }, 1500);
      }
      originalConsoleWarn(...args);
    };

    // Wait for auth to initialize before rendering
    if (!loading) {
      setIsChecking(false);
    }

    // Cleanup
    return () => {
      console.warn = originalConsoleWarn;
    };
  }, [loading, navigate, location.pathname, toast]);

  if (isChecking || hasTokenError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!user || !session) {
    // Redirect to login page with return URL
    return <Navigate to="/auth/login" state={{ from: location.pathname }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
