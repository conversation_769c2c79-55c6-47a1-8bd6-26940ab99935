import { useState, useMemo } from "react";
import { Search, CheckCircle, XCircle, Clock } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { PropertyRequest } from "@/hooks/usePropertyRequests";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";
import { useTranslation } from "@/contexts/TranslationContext";

interface PropertyRequestTableProps {
  requests: PropertyRequest[];
  onUpdateStatus: (id: string, status: PropertyRequest['status']) => void;
}

export function PropertyRequestTable({ requests, onUpdateStatus }: PropertyRequestTableProps) {
  const { isAdmin } = useAuth();
  const { formatPrice } = useExchangeRate();
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<PropertyRequest['status'] | "all">("all");

  // Filter and sort the requests
  const filteredRequests = useMemo(() => {
    return requests.filter((request) => {
      // Apply status filter
      if (statusFilter !== "all" && request.status !== statusFilter) {
        return false;
      }

      // Apply search filter
      const searchLower = searchTerm.toLowerCase();
      return (
        request.property_type.toLowerCase().includes(searchLower) ||
        request.location.toLowerCase().includes(searchLower) ||
        request.details.toLowerCase().includes(searchLower) ||
        request.contact_preference.toLowerCase().includes(searchLower)
      );
    });
  }, [requests, searchTerm, statusFilter]);

  // Function to render status badge
  const renderStatusBadge = (status: PropertyRequest['status']) => {
    switch (status) {
      case 'open':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Clock className="mr-1 h-3 w-3" /> {t('customerRequests.status.open')}
          </Badge>
        );
      case 'in-progress':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="mr-1 h-3 w-3" /> {t('customerRequests.status.inProgress')}
          </Badge>
        );
      case 'matched':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" /> {t('customerRequests.status.matched')}
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <XCircle className="mr-1 h-3 w-3" /> {t('customerRequests.status.closed')}
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center relative flex-1">
          <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('common.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 max-w-sm"
          />
        </div>
        <div className="flex gap-2">
          <Select
            value={statusFilter}
            onValueChange={(value) => setStatusFilter(value as any)}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder={t('properties.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('common.all')} {t('properties.status')}</SelectItem>
              <SelectItem value="open">{t('customerRequests.status.open')}</SelectItem>
              <SelectItem value="in-progress">{t('customerRequests.status.inProgress')}</SelectItem>
              <SelectItem value="matched">{t('customerRequests.status.matched')}</SelectItem>
              <SelectItem value="closed">{t('customerRequests.status.closed')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('customerRequests.propertyType')}</TableHead>
              <TableHead>{t('properties.location')}</TableHead>
              <TableHead>{t('customerRequests.priceRange')}</TableHead>
              <TableHead>{t('customerRequests.contactMethod')}</TableHead>
              <TableHead>{t('properties.status')}</TableHead>
              <TableHead className="text-right">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRequests.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  {t('customerRequests.noRequests')}
                </TableCell>
              </TableRow>
            ) : (
              filteredRequests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell>
                    <div className="font-medium capitalize">{request.property_type}</div>
                    <div className="text-xs text-muted-foreground">
                      {request.min_bedrooms} {t('properties.beds')}, {request.min_bathrooms} {t('properties.baths')}, {request.min_size}م²
                    </div>
                  </TableCell>
                  <TableCell>{request.location}</TableCell>
                  <TableCell>
                    {formatPrice(request.min_price)} - {formatPrice(request.max_price)}
                  </TableCell>
                  <TableCell>{request.contact_preference}</TableCell>
                  <TableCell>{renderStatusBadge(request.status)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {isAdmin() && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              {t('customerRequests.updateStatus')}
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onUpdateStatus(request.id, 'open')}>
                              {t('customerRequests.markAsOpen')}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onUpdateStatus(request.id, 'in-progress')}>
                              {t('customerRequests.markAsInProgress')}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onUpdateStatus(request.id, 'matched')}>
                              {t('customerRequests.markAsMatched')}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onUpdateStatus(request.id, 'closed')}>
                              {t('customerRequests.markAsClosed')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
