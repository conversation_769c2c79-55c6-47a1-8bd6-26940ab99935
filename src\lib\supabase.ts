
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

const supabaseUrl = 'https://xzjlqzklovmwlkxoousf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh6amxxemtsb3Ztd2xreG9vdXNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4MTc3NjIsImV4cCI6MjA2MDM5Mzc2Mn0.oUutoflWPw72Mtj-8fGGgkXt8xuKDYDSfsstjtBI960';

export const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Function to get the current user's ID
export const getCurrentUserId = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  console.log(user?.id);
  console.log(user);
  return user?.id;
};

// Function to check if user is authenticated
export const isAuthenticated = async () => {
  const { data } = await supabase.auth.getSession();
  return !!data.session;
};

// Helper to get current session
export const getCurrentSession = async () => {
  const { data } = await supabase.auth.getSession();
  return data.session;
};

// Helper to get current user
export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

console.log('Supabase client initialized with URL:', supabaseUrl);
