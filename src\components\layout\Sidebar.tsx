
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Home, Building, Settings, Users, Menu, X, List, DollarSign } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useIsMobile } from "@/hooks/use-mobile";
import { useTranslation } from "@/contexts/TranslationContext";

export function Sidebar() {
  const { user, isAdmin } = useAuth();
  const [expanded, setExpanded] = useState(true);
  const isMobile = useIsMobile();
  const { t } = useTranslation();
  const location = useLocation();

  // Automatically collapse on mobile
  const isCollapsed = isMobile ? !expanded : !expanded;

  // Get user initial safely
  const getUserInitial = () => {
    console.log(user?.id);
    console.log(user);
    if (!user) return "?";
    if (user.email) return user.email.charAt(0).toUpperCase();
    return "U";
  };

  // Check if a path is active
  const isActive = (path: string) => location.pathname === path;

  return (
    <>
      {/* Mobile overlay */}
      {isMobile && expanded && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={() => setExpanded(false)}
        />
      )}

      <aside
        className={cn(
          "fixed top-0 bottom-0 z-50 flex flex-col border-r bg-background transition-all duration-300",
          isCollapsed ? "w-16" : "w-64",
          isMobile && !expanded && "translate-x-[-100%]"
        )}
      >
        <div className="flex h-16 items-center border-b px-4 justify-between">
          <div className="flex items-center">
            <Building className="h-6 w-6" />
            {!isCollapsed && <span className="ml-2 font-bold">{t('layout.appName')}</span>}
          </div>
          <Button variant="ghost" size="icon" onClick={() => setExpanded(!expanded)}>
            {isCollapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
          </Button>
        </div>

        <nav className="flex-1 overflow-auto py-4">
          <ul className="space-y-2 px-2">
            <li>
              <Link to="/"
                className={cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-foreground transition-all hover:bg-accent hover:text-accent-foreground",
                  isCollapsed && "justify-center px-0",
                  isActive('/') && "bg-accent text-accent-foreground"
                )}
              >
                <Home className="h-5 w-5" />
                {!isCollapsed && <span>{t('layout.dashboard')}</span>}
              </Link>
            </li>
            <li>
              <Link to="/properties"
                className={cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-foreground transition-all hover:bg-accent hover:text-accent-foreground",
                  isCollapsed && "justify-center px-0",
                  isActive('/properties') && "bg-accent text-accent-foreground"
                )}
              >
                <Building className="h-5 w-5" />
                {!isCollapsed && <span>{t('layout.properties')}</span>}
              </Link>
            </li>
            <li>
              <Link to="/property-offers"
                className={cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-foreground transition-all hover:bg-accent hover:text-accent-foreground",
                  isCollapsed && "justify-center px-0",
                  isActive('/property-offers') && "bg-accent text-accent-foreground"
                )}
              >
                <List className="h-5 w-5" />
                {!isCollapsed && <span>Property Offers</span>}
              </Link>
            </li>
            <li>
              <Link to="/customer-requests"
                className={cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-foreground transition-all hover:bg-accent hover:text-accent-foreground",
                  isCollapsed && "justify-center px-0",
                  isActive('/customer-requests') && "bg-accent text-accent-foreground"
                )}
              >
                <Users className="h-5 w-5" />
                {!isCollapsed && <span>Customer Requests</span>}
              </Link>
            </li>
            {isAdmin() && (
              <li>
                <Link to="/settings"
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-foreground transition-all hover:bg-accent hover:text-accent-foreground",
                    isCollapsed && "justify-center px-0",
                    isActive('/settings') && "bg-accent text-accent-foreground"
                  )}
                >
                  <Settings className="h-5 w-5" />
                  {!isCollapsed && <span>{t('layout.settings')}</span>}
                </Link>
              </li>
            )}
          </ul>
        </nav>

        <div className="mt-auto border-t p-4">
          <div className={cn(
            "flex items-center gap-3",
            isCollapsed && "justify-center"
          )}>
            <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
              {getUserInitial()}
            </div>
            {!isCollapsed && (
              <div>
                <p className="text-sm font-medium">{user?.email || t('common.guest')}</p>
                <p className="text-xs text-muted-foreground capitalize">{user ? t('common.user') : t('common.notLoggedIn')}</p>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* Toggle button for mobile */}
      {isMobile && !expanded && (
        <Button
          variant="outline"
          size="icon"
          className="fixed top-4 left-4 z-50"
          onClick={() => setExpanded(true)}
        >
          <Menu className="h-5 w-5" />
        </Button>
      )}
    </>
  );
}
