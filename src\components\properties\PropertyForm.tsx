
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslation } from "@/contexts/TranslationContext";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Property, PropertyType } from "@/types/property";
import { ImageUpload } from "./ImageUpload";

const formSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  description: z.string().min(10, { message: "Description must be at least 10 characters" }),
  price: z.coerce.number().positive({ message: "Price must be a positive number" }),
  location: z.string().min(3, { message: "Location must be at least 3 characters" }),
  bedrooms: z.coerce.number().min(0).int(),
  bathrooms: z.coerce.number().min(0).int(),
  floor: z.coerce.number().min(-1).int().nullable().optional(),
  size: z.coerce.number().positive({ message: "Size must be a positive number" }), // Changed from 'area' to 'size'
  furnished: z.enum(["furnished", "semi-furnished", "unfurnished"]),
  type: z.enum(["apartment", "house", "villa", "land", "commercial", "other"]),
  status: z.enum(["for-sale", "for-rent"]),
  images: z.string().array().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface PropertyFormProps {
  property?: Property;
  onSubmit: (data: FormValues) => void;
  isSubmitting?: boolean;
}

export function PropertyForm({ property, onSubmit, isSubmitting = false }: PropertyFormProps) {
  const navigate = useNavigate();
  const [selectedType, setSelectedType] = useState<PropertyType>(
    property?.type || "apartment"
  );
  const { t } = useTranslation();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: property ? {
      ...property,
      // Make sure the price is a number
      price: Number(property.price),
      size: Number(property.size), // Changed from 'area' to 'size'
      bedrooms: Number(property.bedrooms),
      bathrooms: Number(property.bathrooms),
      floor: property.floor !== null ? Number(property.floor) : null,
    } : {
      title: "",
      description: "",
      price: 0,
      location: "",
      bedrooms: 0,
      bathrooms: 0,
      floor: 0,
      size: 0, // Changed from 'area' to 'size'
      furnished: "unfurnished",
      type: "apartment",
      status: "for-sale",
      images: [],
    },
  });

  function handleSubmit(data: FormValues) {
    onSubmit(data);
  }

  // Show or hide bedrooms/bathrooms/floor/furnished based on property type
  const showLivingDetails = selectedType !== "land";

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>{t('propertyForm.propertyImages')}</CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="images"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <ImageUpload
                        images={field.value || []}
                        onImagesChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('propertyForm.basicInfo')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('propertyForm.propertyTitle')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('propertyForm.enterTitle')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('propertyForm.description')}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('propertyForm.describeProperty')}
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('propertyForm.status')}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={`${t('common.select')} ${t('properties.status')}`} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="for-sale">{t('properties.forSale')}</SelectItem>
                          <SelectItem value="for-rent">{t('properties.forRent')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('propertyForm.price')} ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="1"
                          {...field}
                          onChange={(e) => {
                            field.onChange(e.target.valueAsNumber);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('propertyForm.location')}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('propertyForm.enterLocation')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('propertyForm.details')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('properties.type')}</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        setSelectedType(value as PropertyType);
                        field.onChange(value);
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={`${t('common.select')} ${t('properties.type')}`} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="apartment">{t('properties.apartment')}</SelectItem>
                        <SelectItem value="house">{t('properties.house')}</SelectItem>
                        <SelectItem value="villa">{t('properties.villa')}</SelectItem>
                        <SelectItem value="land">{t('properties.land')}</SelectItem>
                        <SelectItem value="commercial">{t('properties.commercial')}</SelectItem>
                        <SelectItem value="other">{t('properties.other')}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="size"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('propertyForm.size')}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e.target.valueAsNumber);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {showLivingDetails && (
                <>
                  <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
                    <FormField
                      control={form.control}
                      name="bedrooms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('propertyForm.bedrooms')}</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              step="1"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e.target.valueAsNumber);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="bathrooms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('propertyForm.bathrooms')}</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              step="0.5"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e.target.valueAsNumber);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {(selectedType === "apartment" || selectedType === "commercial") && (
                      <FormField
                        control={form.control}
                        name="floor"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('propertyForm.floor')}</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="-1"
                                step="1"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(e.target.valueAsNumber);
                                }}
                                value={field.value === null ? "" : field.value}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  <FormField
                    control={form.control}
                    name="furnished"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('properties.furnished')}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={`${t('common.select')} ${t('properties.furnished')}`} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="furnished">{t('properties.furnished')}</SelectItem>
                            <SelectItem value="semi-furnished">{t('properties.semiFurnished')}</SelectItem>
                            <SelectItem value="unfurnished">{t('properties.unfurnished')}</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <CardFooter className="flex justify-between px-0">
          <Button type="button" variant="outline" onClick={() => navigate(-1)}>
            {t('common.cancel')}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? t('propertyForm.submitting') : property ? t('propertyForm.editProperty') : t('propertyForm.addNew')}
          </Button>
        </CardFooter>
      </form>
    </Form>
  );
}
