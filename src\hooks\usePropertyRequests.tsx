import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';

// Define the PropertyRequest type
export interface PropertyRequest {
  id: string;
  property_type: string;
  location: string;
  min_price: number;
  max_price: number;
  min_bedrooms: number;
  min_bathrooms: number;
  min_size: number;
  details: string;
  contact_preference: string;
  status: 'open' | 'in-progress' | 'closed' | 'matched';
  created_by: string;
  created_at: string;
  updated_at: string;
}

// Transform the database request format to our application format
const transformRequest = (dbRequest: any): PropertyRequest => {
  return {
    id: dbRequest.id,
    property_type: dbRequest.property_type,
    location: dbRequest.location,
    min_price: dbRequest.min_price,
    max_price: dbRequest.max_price,
    min_bedrooms: dbRequest.min_bedrooms,
    min_bathrooms: dbRequest.min_bathrooms,
    min_size: dbRequest.min_size,
    details: dbRequest.details,
    contact_preference: dbRequest.contact_preference,
    status: dbRequest.status,
    created_by: dbRequest.created_by,
    created_at: dbRequest.created_at,
    updated_at: dbRequest.updated_at || dbRequest.created_at,
  };
};

// Hook to fetch all property requests
export const usePropertyRequests = () => {
  return useQuery({
    queryKey: ['propertyRequests'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('property_requests')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return data.map(transformRequest);
    }
  });
};

// Hook to fetch a single property request
export const usePropertyRequest = (id: string) => {
  return useQuery({
    queryKey: ['propertyRequest', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('property_requests')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      
      return transformRequest(data);
    },
    enabled: !!id
  });
};

// Hook to update a property request status
export const useUpdatePropertyRequestStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, status }: { id: string; status: PropertyRequest['status'] }) => {
      const { data, error } = await supabase
        .from('property_requests')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      
      return transformRequest(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['propertyRequests'] });
    }
  });
};

// Hook to delete a property request
export const useDeletePropertyRequest = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('property_requests')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['propertyRequests'] });
    }
  });
};
