import React, { createContext, use<PERSON>ontext, ReactNode } from 'react';
import { ar } from '../translations/ar';

// Define the shape of our translation context
type TranslationContextType = {
  t: (key: string) => string;
};

// Create the context with a default value
const TranslationContext = createContext<TranslationContextType>({
  t: () => '',
});

// Create a provider component
export const TranslationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Function to get a translation by key
  const t = (key: string): string => {
    // Split the key by dots to navigate the nested structure
    const keys = key.split('.');
    
    // Start with the Arabic translations
    let value: any = ar;
    
    // Navigate through the nested structure
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // If the key doesn't exist, return the key itself
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
    }
    
    // Return the translation or the key if the value is not a string
    return typeof value === 'string' ? value : key;
  };
  
  return (
    <TranslationContext.Provider value={{ t }}>
      {children}
    </TranslationContext.Provider>
  );
};

// Create a hook to use the translation context
export const useTranslation = () => {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  
  return context;
};
