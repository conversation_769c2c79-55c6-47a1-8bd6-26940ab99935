
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { PropertyForm } from "@/components/properties/PropertyForm";
import { useToast } from "@/components/ui/use-toast";
import { useCreateProperty } from "@/hooks/useProperties";
import { Property, PropertyType, PropertyStatus, FurnishedStatus } from "@/types/property";
import { useTranslation } from "@/contexts/TranslationContext";
import { getCurrentSession } from "@/lib/supabase";

const AddProperty = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const createPropertyMutation = useCreateProperty();
  const { t } = useTranslation();

  const handleSubmit = async (values: Omit<Property, "id" | "created_by" | "createdAt" | "updatedAt">) => {
    setIsSubmitting(true);
    
    // Check authentication
    const session = await getCurrentSession();
    if (!session) {
      toast({
        title: t('common.error'),
        description: t('auth.notAuthenticated'),
        variant: "destructive",
      });
      navigate('/auth/login');
      return;
    }

    createPropertyMutation.mutate(
      {
        ...values
      },
      {
        onSuccess: () => {
          toast({
            title: t('properties.added'),
            description: t('properties.addedSuccess'),
          });
          navigate("/properties");
        },
        onError: (error) => {
          console.error('Error adding property:', error);
          
          // Check for RLS or auth error
          if (error.message && error.message.includes('row-level security policy')) {
            toast({
              title: t('common.error'),
              description: t('properties.rls'),
              variant: "destructive",
            });
          } else if (error.message && error.message.includes('auth')) {
            toast({
              title: t('common.error'),
              description: t('auth.notAuthenticated'),
              variant: "destructive",
            });
            navigate('/auth/login');
          } else {
            // Generic error
            toast({
              title: t('common.error'),
              description: `${t('properties.addError')}: ${error.message}`,
              variant: "destructive",
            });
          }
        },
        onSettled: () => {
          setIsSubmitting(false);
        }
      }
    );
  };

  return (
    <Layout>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">{t('properties.add')}</h1>
        <PropertyForm onSubmit={handleSubmit} isSubmitting={isSubmitting} />
      </div>
    </Layout>
  );
};

export default AddProperty;
