
import { ChangeEvent, useCallback, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ImagePlus, X } from "lucide-react";
import { fileToBase64, validateBase64Image } from "@/lib/utils";
import { useTranslation } from "@/contexts/TranslationContext";
import { Spinner } from "../ui/spinner";

interface ImageUploadProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  maxImages?: number;
}

export function ImageUpload({ images, onImagesChange, maxImages = 5 }: ImageUploadProps) {
  // Ensure images is an array
  const imageArray = Array.isArray(images) ? images : [];
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();

  const handleImageUpload = useCallback(async (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    setIsLoading(true);
    try {
      // Convert all files to Base64 strings
      const base64Promises = files.map(file => fileToBase64(file));
      const base64Images = await Promise.all(base64Promises);

      onImagesChange([...imageArray, ...base64Images].slice(0, maxImages));
    } catch (error) {
      console.error('Error converting images to Base64:', error);
    } finally {
      setIsLoading(false);
    }
  }, [imageArray, onImagesChange, maxImages]);

  const removeImage = useCallback((index: number) => {
    const newImages = imageArray.filter((_, i) => i !== index);
    onImagesChange(newImages);
  }, [imageArray, onImagesChange]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
        {imageArray.map((image, index) => (
          <div key={index} className="group relative aspect-square overflow-hidden rounded-lg border">
            <img
              src={validateBase64Image(image)}
              alt={`Property ${index + 1}`}
              className="h-full w-full object-cover"
            />
            <Button
              variant="destructive"
              size="icon"
              className="absolute right-2 top-2 h-6 w-6 opacity-0 transition-opacity group-hover:opacity-100"
              onClick={() => removeImage(index)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}

        {imageArray.length < maxImages && (
          <div className="aspect-square">
            <label
              htmlFor="image-upload"
              className="flex h-full w-full cursor-pointer items-center justify-center rounded-lg border-2 border-dashed text-muted-foreground hover:border-muted-foreground/50"
            >
              <div className="flex flex-col items-center gap-2">
                {isLoading ? (
                  <Spinner className="h-8 w-8" />
                ) : (
                  <>
                    <ImagePlus className="h-8 w-8" />
                    <span className="text-xs">{t('propertyForm.addImage')}</span>
                  </>
                )}
              </div>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                onChange={handleImageUpload}
                disabled={isLoading}
              />
            </label>
          </div>
        )}
      </div>
      <p className="text-xs text-muted-foreground">
        {t('propertyForm.uploadFormats').replace('{maxImages}', maxImages.toString())}
      </p>
    </div>
  );
}
