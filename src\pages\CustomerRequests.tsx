import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { supabase, getCurrentUserId } from "@/lib/supabase";
import { useTranslation } from "@/contexts/TranslationContext";
import {
  usePropertyRequests,
  useUpdatePropertyRequestStatus,
} from "@/hooks/usePropertyRequests";
import { PropertyRequestTable } from "@/components/property-requests/PropertyRequestTable";
import { PropertyRequestCard } from "@/components/property-requests/PropertyRequestCard";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Spinner } from "@/components/ui/spinner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Define the form schema with zod
const requestFormSchema = z.object({
  property_type: z.string().min(1, { message: "Property type is required" }),
  location: z.string().min(1, { message: "Location is required" }),
  min_price: z.coerce.number().min(0, { message: "Price must be positive" }),
  max_price: z.coerce.number().min(0, { message: "Price must be positive" }),
  min_bedrooms: z.coerce.number().min(0, { message: "Must be 0 or more" }),
  min_bathrooms: z.coerce.number().min(0, { message: "Must be 0 or more" }),
  min_size: z.coerce.number().min(0, { message: "Size must be positive" }),
  details: z.string().min(5, { message: "Please provide some details" }),
  contact_preference: z
    .string()
    .min(1, { message: "Contact preference is required" }),
});

type RequestFormValues = z.infer<typeof requestFormSchema>;

const CustomerRequests = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Fetch property requests
  const {
    data: propertyRequests = [],
    isLoading: isLoadingRequests,
    error: requestsError,
  } = usePropertyRequests();

  // Update property request status
  const updateStatusMutation = useUpdatePropertyRequestStatus();

  // Handle updating request status
  const handleUpdateStatus = async (id: string, status: string) => {
    try {
      await updateStatusMutation.mutateAsync({ id, status: status as any });
      toast({
        title: t('customerRequests.statusUpdated'),
        description: t('customerRequests.statusUpdatedDesc').replace('{status}', status),
      });
    } catch (error: any) {
      console.error("Error updating status:", error);
      toast({
        title: t('customerRequests.error'),
        description: `${t('customerRequests.updateStatusError')}: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const form = useForm<RequestFormValues>({
    resolver: zodResolver(requestFormSchema),
    defaultValues: {
      property_type: "",
      location: "",
      min_price: 0,
      max_price: 0,
      min_bedrooms: 0,
      min_bathrooms: 0,
      min_size: 0,
      details: "",
      contact_preference: "",
    },
  });

  const onSubmit = async (values: RequestFormValues) => {
    setIsSubmitting(true);
    try {
      const userId = await getCurrentUserId();

      if (!userId) {
        toast({
          title: t('customerRequests.authError'),
          description: t('customerRequests.authErrorDesc'),
          variant: "destructive",
        });
        navigate("/auth/login");
        return;
      }

      const { error } = await supabase.from("property_requests").insert({
        ...values,
        created_by: userId,
        status: "open",
      });

      if (error) {
        console.error("Error submitting request:", error);
        throw error;
      }

      toast({
        title: t('customerRequests.requestSubmitted'),
        description: t('customerRequests.requestSubmittedDesc'),
      });

      form.reset();
    } catch (error: any) {
      toast({
        title: t('customerRequests.error'),
        description: `${t('customerRequests.errorDesc')} ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">{t('customerRequests.title')}</h1>
        <Card>
          <CardHeader>
            <CardTitle>{t('customerRequests.subtitle')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="property_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customerRequests.propertyType')}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('customerRequests.selectPropertyType')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="apartment">{t('properties.apartment')}</SelectItem>
                            <SelectItem value="house">{t('properties.house')}</SelectItem>
                            <SelectItem value="villa">{t('properties.villa')}</SelectItem>
                            <SelectItem value="commercial">
                              {t('properties.commercial')}
                            </SelectItem>
                            <SelectItem value="land">{t('properties.land')}</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customerRequests.preferredLocation')}</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder={t('customerRequests.enterLocation')} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="min_price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customerRequests.minPrice')}</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="max_price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customerRequests.maxPrice')}</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="min_bedrooms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customerRequests.minBedrooms')}</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="min_bathrooms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customerRequests.minBathrooms')}</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="min_size"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customerRequests.minSize')}</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contact_preference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('customerRequests.contactPreference')}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('customerRequests.selectContactMethod')} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="email">{t('customerRequests.email')}</SelectItem>
                            <SelectItem value="phone">{t('customerRequests.phone')}</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="details"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('customerRequests.additionalDetails')}</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder={t('customerRequests.detailsPlaceholder')}
                          className="min-h-[120px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full md:w-auto"
                >
                  {isSubmitting ? (
                    <>
                      <Spinner className="mr-2 h-4 w-4" />
                      {t('customerRequests.submitting')}
                    </>
                  ) : (
                    t('customerRequests.submitRequest')
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Property Requests List */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold mb-4">{t('customerRequests.requestsList')}</h2>

          {isLoadingRequests ? (
            <div className="flex justify-center items-center h-40">
              <Spinner className="h-8 w-8" />
            </div>
          ) : requestsError ? (
            <div className="bg-red-50 p-4 rounded-md text-red-600">
              <p>{t('customerRequests.error')}: {(requestsError as Error).message}</p>
            </div>
          ) : (
            <Tabs defaultValue="table" className="w-full">
              <div className="flex justify-between items-center mb-4">
                <TabsList>
                  <TabsTrigger value="table">{t('customerRequests.tableView')}</TabsTrigger>
                  <TabsTrigger value="cards">{t('customerRequests.cardView')}</TabsTrigger>
                </TabsList>
                <div className="text-sm text-muted-foreground">
                  {t('customerRequests.total')}: {propertyRequests.length} {t('customerRequests.requests')}
                </div>
              </div>

              <TabsContent value="table" className="mt-0">
                <PropertyRequestTable
                  requests={propertyRequests}
                  onUpdateStatus={handleUpdateStatus}
                />
              </TabsContent>

              <TabsContent value="cards" className="mt-0">
                <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {propertyRequests.map(request => (
                    <PropertyRequestCard
                      key={request.id}
                      request={request}
                      onUpdateStatus={handleUpdateStatus}
                    />
                  ))}

                  {propertyRequests.length === 0 && (
                    <div className="col-span-full text-center p-8 bg-muted rounded-md">
                      <p className="text-muted-foreground">{t('customerRequests.noRequests')}</p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default CustomerRequests;
