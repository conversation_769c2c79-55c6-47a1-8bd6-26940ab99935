
import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { supabase, getCurrentUserId } from "@/lib/supabase";
import { useTranslation } from "@/contexts/TranslationContext";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Spinner } from "@/components/ui/spinner";

// Define the form schema with zod
const requestFormSchema = z.object({
  property_type: z.string().min(1, { message: "Property type is required" }),
  location: z.string().min(1, { message: "Location is required" }),
  min_price: z.coerce.number().min(0, { message: "Price must be positive" }),
  max_price: z.coerce.number().min(0, { message: "Price must be positive" }),
  min_bedrooms: z.coerce.number().min(0, { message: "Must be 0 or more" }),
  min_bathrooms: z.coerce.number().min(0, { message: "Must be 0 or more" }),
  min_size: z.coerce.number().min(0, { message: "Size must be positive" }),
  details: z.string().min(5, { message: "Please provide some details" }),
  contact_preference: z.string().min(1, { message: "Contact preference is required" }),
});

type RequestFormValues = z.infer<typeof requestFormSchema>;

const CustomerRequests = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const form = useForm<RequestFormValues>({
    resolver: zodResolver(requestFormSchema),
    defaultValues: {
      property_type: "",
      location: "",
      min_price: 0,
      max_price: 0,
      min_bedrooms: 0,
      min_bathrooms: 0,
      min_size: 0,
      details: "",
      contact_preference: "",
    },
  });

  const onSubmit = async (values: RequestFormValues) => {
    setIsSubmitting(true);
    try {
      const userId = await getCurrentUserId();
      
      if (!userId) {
        toast({
          title: "Authentication Error",
          description: "You must be logged in to submit a request",
          variant: "destructive",
        });
        navigate("/auth/login");
        return;
      }

      const { error } = await supabase.from("property_requests").insert({
        ...values,
        created_by: userId,
        status: "open"
      });

      if (error) {
        console.error("Error submitting request:", error);
        throw error;
      }

      toast({
        title: "Request Submitted",
        description: "Your property request has been submitted successfully.",
      });

      form.reset();
      
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to submit request: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Submit a Property Request</h1>
        
        <Card>
          <CardHeader>
            <CardTitle>Tell us what you're looking for</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="property_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Property Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select property type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="apartment">Apartment</SelectItem>
                            <SelectItem value="house">House</SelectItem>
                            <SelectItem value="villa">Villa</SelectItem>
                            <SelectItem value="commercial">Commercial</SelectItem>
                            <SelectItem value="land">Land</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preferred Location</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter location" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="min_price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Price (USD)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="max_price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum Price (USD)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="min_bedrooms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Bedrooms</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="min_bathrooms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Bathrooms</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="min_size"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Size (m²)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} placeholder="0" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="contact_preference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preferred Contact Method</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select contact method" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="phone">Phone</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="details"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Details</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Please describe any additional requirements or preferences..."
                          className="min-h-[120px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button type="submit" disabled={isSubmitting} className="w-full md:w-auto">
                  {isSubmitting ? (
                    <>
                      <Spinner className="mr-2 h-4 w-4" />
                      Submitting...
                    </>
                  ) : (
                    "Submit Request"
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default CustomerRequests;
