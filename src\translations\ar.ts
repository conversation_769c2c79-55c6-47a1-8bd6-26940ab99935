// Arabic translations
export const ar = {
  // Common
  common: {
    search: "بحث",
    submit: "إرسال",
    cancel: "إلغاء",
    save: "حفظ",
    edit: "تعديل",
    delete: "حذف",
    back: "رجوع",
    loading: "جاري التحميل...",
    error: "خطأ",
    success: "نجاح",
    welcome: "مرحبًا",
    yes: "نعم",
    no: "لا",
    more: "المزيد",
    all: "الكل",
    actions: "إجراءات",
    details: "التفاصيل",
    view: "عرض",
    close: "إغلاق",
    confirm: "تأكيد",
    guest: "زائر",
    user: "مستخدم",
    notLoggedIn: "غير مسجل الدخول",
    select: "اختر",
  },

  // Layout
  layout: {
    dashboard: "لوحة التحكم",
    properties: "العقارات",
    settings: "الإعدادات",
    users: "المستخدمين",
    logout: "تسجيل الخروج",
    appName: "العقارات برو",
  },

  // Auth
  auth: {
    login: "تسجيل الدخول",
    register: "إنشاء حساب",
    email: "البريد الإلكتروني",
    password: "كلمة المرور",
    confirmPassword: "تأكيد كلمة المرور",
    forgotPassword: "نسيت كلمة المرور؟",
    loginToAccount: "تسجيل الدخول إلى حسابك",
    createAccount: "إنشاء حساب جديد",
    signUpToStart: "سجل للبدء في إدارة العقارات",
    allFieldsRequired: "جميع الحقول مطلوبة",
    passwordsDoNotMatch: "كلمات المرور غير متطابقة",
    passwordMinLength: "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
    authRequired: "مطلوب المصادقة",
    loginRequired: "يجب تسجيل الدخول للوصول إلى هذه الصفحة",
    dontHaveAccount: "ليس لديك حساب؟",
    alreadyHaveAccount: "لديك حساب بالفعل؟",
  },

  // Dashboard
  dashboard: {
    title: "لوحة التحكم",
    welcomeBack: "مرحبًا بعودتك",
    totalProperties: "إجمالي العقارات",
    forSale: "للبيع",
    forRent: "للإيجار",
    totalValue: "القيمة الإجمالية",
    recentProperties: "العقارات الحديثة",
    featuredProperties: "العقارات المميزة",
    viewAll: "عرض الكل",
    addProperty: "إضافة عقار",
    highlightedProperties: "العقارات البارزة في محفظتك",
  },

  // Properties
  properties: {
    title: "العقارات",
    addProperty: "إضافة عقار",
    propertyListings: "قائمة العقارات",
    viewAndManage: "عرض وإدارة جميع العقارات",
    tableView: "عرض الجدول",
    gridView: "عرض الشبكة",
    total: "الإجمالي",
    noProperties: "لا توجد عقارات",
    propertyTitle: "عنوان العقار",
    status: "الحالة",
    type: "النوع",
    location: "الموقع",
    price: "السعر",
    forSale: "للبيع",
    forRent: "للإيجار",
    apartment: "شقة",
    house: "منزل",
    villa: "فيلا",
    land: "أرض",
    commercial: "تجاري",
    other: "أخرى",
    beds: "غرف",
    bed: "غرفة",
    baths: "حمامات",
    bath: "حمام",
    size: "المساحة",
    floor: "الطابق",
    furnished: "مفروش",
    semiFurnished: "شبه مفروش",
    unfurnished: "غير مفروش",
    month: "شهر",
    managePortfolio: "إدارة محفظة العقارات الخاصة بك",
    deleted: "تم حذف العقار",
    deletedSuccess: "تم حذف العقار بنجاح",
    deleteError: "فشل في حذف العقار",
    loadError: "خطأ في تحميل العقارات",
  },

  // Property Details
  propertyDetails: {
    title: "تفاصيل العقار",
    description: "الوصف",
    features: "المميزات",
    location: "الموقع",
    notes: "الملاحظات",
    addNote: "إضافة ملاحظة",
    deleteProperty: "حذف العقار",
    editProperty: "تعديل العقار",
    confirmDelete: "هل أنت متأكد من حذف هذا العقار؟",
    deleteWarning: "لا يمكن التراجع عن هذا الإجراء.",
    propertyNotFound: "العقار غير موجود",
    propertyNotFoundDesc: "العقار الذي تبحث عنه غير موجود أو تم حذفه.",
    backToProperties: "العودة إلى العقارات",
    overview: "نظرة عامة",
    gallery: "معرض الصور",
    writeNote: "اكتب ملاحظة...",
    sendNote: "إرسال",
    noNotes: "لا توجد ملاحظات بعد",
    addedBy: "أضيف بواسطة",
  },

  // Add/Edit Property
  propertyForm: {
    addNew: "إضافة عقار جديد",
    editProperty: "تعديل العقار",
    createListing: "إنشاء قائمة عقار جديدة مع التفاصيل",
    updateListing: "تحديث قائمة العقار مع التفاصيل",
    basicInfo: "المعلومات الأساسية",
    propertyTitle: "عنوان العقار",
    enterTitle: "أدخل عنوان العقار",
    description: "الوصف",
    describeProperty: "وصف العقار",
    price: "السعر",
    enterPrice: "أدخل السعر",
    location: "الموقع",
    enterLocation: "أدخل الموقع",
    details: "التفاصيل",
    bedrooms: "غرف النوم",
    bathrooms: "الحمامات",
    floor: "الطابق",
    size: "المساحة (م²)",
    status: "الحالة",
    type: "النوع",
    furnished: "مفروش",
    images: "الصور",
    uploadImages: "تحميل الصور",
    dragAndDrop: "اسحب وأفلت الصور هنا أو انقر للتصفح",
    maxSize: "الحد الأقصى للحجم: 5 ميجابايت لكل صورة",
    authRequired: "مطلوب المصادقة",
    loginRequired: "يجب تسجيل الدخول لإنشاء عقار.",
    errorOccurred: "حدث خطأ",
    submitting: "جاري الإرسال...",
    propertyImages: "صور العقار",
    addImage: "إضافة صورة",
    uploadFormats: "تحميل حتى {maxImages} صور. الصيغ المدعومة: JPG، PNG، GIF",
  },

  // Not Found
  notFound: {
    title: "404",
    message: "عفوًا! الصفحة غير موجودة",
    returnHome: "العودة إلى الصفحة الرئيسية",
  },

  // Validation
  validation: {
    required: "هذا الحقل مطلوب",
    minLength: "يجب أن يكون على الأقل {min} أحرف",
    maxLength: "يجب أن يكون أقل من {max} حرف",
    email: "يرجى إدخال بريد إلكتروني صالح",
    number: "يرجى إدخال رقم صالح",
    positive: "يجب أن يكون رقمًا موجبًا",
    integer: "يجب أن يكون رقمًا صحيحًا",
    match: "يجب أن تتطابق القيم",
  },
};
