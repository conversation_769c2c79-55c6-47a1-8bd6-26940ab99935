
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { TranslationProvider } from "@/contexts/TranslationContext";
import { ExchangeRateProvider } from "@/contexts/ExchangeRateContext";
import Index from "./pages/Index";
import Properties from "./pages/Properties";
import PropertyOffers from "./pages/PropertyOffers";
import CustomerRequests from "./pages/CustomerRequests";
import Settings from "./pages/Settings";
import PropertyDetails from "./pages/PropertyDetails";
import AddProperty from "./pages/AddProperty";
import EditProperty from "./pages/EditProperty";
import NotFound from "./pages/NotFound";
import Login from "./pages/Auth/Login";
import Register from "./pages/Auth/Register";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import { handleAuthError } from "./lib/authUtils";
import { supabase } from "./lib/supabase";

// Configure global error handling for Supabase auth errors
supabase.auth.onAuthStateChange((event) => {
  console.log("Auth state changed:", event);

  if (event === 'TOKEN_REFRESHED') {
    console.log('Token refreshed successfully');
  } else if (event === 'SIGNED_OUT') {
    // Clear any stored tokens on sign out
    localStorage.removeItem('supabase.auth.token');
  }
});

// Create a query client with error handling
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry if it's an auth error
        if (error?.message?.includes('Invalid Refresh Token')) {
          return false;
        }
        // Otherwise retry up to 3 times
        return failureCount < 3;
      }
    },
    mutations: {
      onError: (error: any) => {
        // Handle auth errors globally
        handleAuthError(error);
      }
    }
  }
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <TranslationProvider>
          <ExchangeRateProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/properties" element={<Properties />} />
                <Route path="/property-offers" element={<PropertyOffers />} />
                <Route path="/properties/:id" element={<PropertyDetails />} />

                {/* Protected routes */}
                <Route
                  path="/properties/add"
                  element={
                    <ProtectedRoute>
                      <AddProperty />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/properties/:id/edit"
                  element={
                    <ProtectedRoute>
                      <EditProperty />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/customer-requests"
                  element={
                    <ProtectedRoute>
                      <CustomerRequests />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/settings"
                  element={
                    <ProtectedRoute>
                      <Settings />
                    </ProtectedRoute>
                  }
                />

                {/* Auth routes */}
                <Route path="/auth/login" element={<Login />} />
                <Route path="/auth/register" element={<Register />} />

                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </ExchangeRateProvider>
        </TranslationProvider>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
