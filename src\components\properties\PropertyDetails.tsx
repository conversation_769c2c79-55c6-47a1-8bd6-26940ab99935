
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Building,
  MapPin,
  AreaChart,
  Hotel,
  Bath,
  Trash2,
  Edit,
  ArrowLeft,
  Send,
  User,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Property, PropertyNote } from "@/types/property";
import { useAuth } from "@/contexts/AuthContext";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ImageGallery } from "./ImageGallery";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";

interface PropertyDetailsProps {
  property: Property;
  notes: PropertyNote[];
  onAddNote: (note: string) => void;
  onDelete: () => void;
}

export function PropertyDetails({
  property,
  notes,
  onAddNote,
  onDelete,
}: PropertyDetailsProps) {
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();
  const [newNote, setNewNote] = useState("");
  const { formatPrice } = useExchangeRate();

  const handleAddNote = () => {
    if (newNote.trim()) {
      onAddNote(newNote.trim());
      setNewNote("");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate(-1)}
          className="flex gap-2"
        >
          <ArrowLeft className="h-4 w-4" /> Back
        </Button>

        {isAdmin() && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/properties/${property.id}/edit`)}
            >
              <Edit className="mr-2 h-4 w-4" /> Edit
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    this property and its associated data.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={onDelete}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <ImageGallery images={property.images} />

          <Tabs defaultValue="details">
            <TabsList>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="notes">
                Notes ({notes.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>{property.description}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Property Features</CardTitle>
                </CardHeader>
                <CardContent className="grid gap-2 sm:grid-cols-2">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span>
                      Type:{" "}
                      <span className="font-medium capitalize">
                        {property.type}
                      </span>
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <AreaChart className="h-4 w-4 text-muted-foreground" />
                    <span>
                      Size: <span className="font-medium">{property.size} m²</span>
                    </span>
                  </div>

                  {property.type !== "land" && (
                    <>
                      <div className="flex items-center gap-2">
                        <Hotel className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Bedrooms:{" "}
                          <span className="font-medium">{property.bedrooms}</span>
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Bath className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Bathrooms:{" "}
                          <span className="font-medium">
                            {property.bathrooms}
                          </span>
                        </span>
                      </div>

                      {(property.type === "apartment" ||
                        property.type === "commercial") && (
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          <span>
                            Floor:{" "}
                            <span className="font-medium">
                              {property.floor !== null ? property.floor : "N/A"}
                            </span>
                          </span>
                        </div>
                      )}

                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Furnished:{" "}
                          <span className="font-medium capitalize">
                            {property.furnished}
                          </span>
                        </span>
                      </div>
                    </>
                  )}

                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>
                      Location:{" "}
                      <span className="font-medium">{property.location}</span>
                    </span>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notes">
              <Card>
                <CardHeader>
                  <CardTitle>Property Notes</CardTitle>
                  <CardDescription>
                    Internal notes about this property.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {notes.length === 0 ? (
                    <div className="text-center p-4 text-muted-foreground">
                      No notes yet.
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {notes.map((note) => (
                        <div
                          key={note.id}
                          className="rounded-lg border p-4 space-y-2"
                        >
                          <p className="text-sm">{note.text}</p>
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>{note.created_by}</span>
                            </div>
                            <div>{formatDate(note.created_at)}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {isAdmin() && (
                    <div className="flex gap-2 mt-4">
                      <Textarea
                        placeholder="Add a note about this property..."
                        className="flex-1"
                        value={newNote}
                        onChange={(e) => setNewNote(e.target.value)}
                      />
                      <Button onClick={handleAddNote}>
                        <Send className="mr-2 h-4 w-4" /> Add Note
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{property.title}</CardTitle>
              <CardDescription className="flex items-center gap-1">
                <MapPin className="h-4 w-4" /> {property.location}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-4">
                {formatPrice(property.price)}
                {property.status === "for-rent" && (
                  <span className="text-sm font-normal">/month</span>
                )}
              </div>

              <div className="grid grid-cols-2 gap-y-2 text-sm">
                <div>Status</div>
                <div className="font-medium capitalize">
                  {property.status === "for-sale" ? "For Sale" : "For Rent"}
                </div>

                <div>Property Type</div>
                <div className="font-medium capitalize">{property.type}</div>

                <div>Created</div>
                <div className="font-medium">
                  {formatDate(property.createdAt)}
                </div>

                <div>Last Updated</div>
                <div className="font-medium">
                  {formatDate(property.updatedAt)}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
