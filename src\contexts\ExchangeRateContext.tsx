import React, { createContext, useState, useEffect, useContext } from 'react';

interface ExchangeRateContextProps {
  currency: string;
  setCurrency: (currency: string) => void;
  exchangeRate: number;
  refreshExchangeRate: () => Promise<void>;
  lastUpdated: string | null;
  formatPrice: (price: number) => string;
}

const ExchangeRateContext = createContext<ExchangeRateContextProps>({
  currency: 'USD',
  setCurrency: () => {},
  exchangeRate: 1,
  refreshExchangeRate: async () => {},
  lastUpdated: null,
  formatPrice: (price: number) => `$${price.toLocaleString()}`,
});

const fetchExchangeRate = async (): Promise<number> => {
  try {
    const response = await fetch('/api/exchange-rate');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data.rate;
  } catch (error) {
    console.error('Failed to fetch exchange rate:', error);
    // Provide a default value or re-throw the error as needed
    throw error;
  }
};

export const ExchangeRateProvider = ({ children }: { children: React.ReactNode }) => {
  const [currency, setCurrency] = useState<string>('USD');
  const [exchangeRate, setExchangeRate] = useState<number>(1);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);

  useEffect(() => {
    const storedRate = localStorage.getItem('exchangeRate');
    const storedLastUpdated = localStorage.getItem('exchangeRateLastUpdated');

    if (storedRate && storedLastUpdated) {
      setExchangeRate(parseFloat(storedRate));
      setLastUpdated(storedLastUpdated);
    } else {
      refreshExchangeRate();
    }
  }, []);

  // Update this function to return void instead of number
  const refreshExchangeRate = async (): Promise<void> => {
    try {
      const rate = await fetchExchangeRate();
      setExchangeRate(rate);
      // Store in localStorage
      localStorage.setItem('exchangeRate', rate.toString());
      localStorage.setItem('exchangeRateLastUpdated', new Date().toISOString());
      setLastUpdated(new Date().toISOString());
    } catch (error) {
      console.error('Failed to fetch exchange rate:', error);
    }
    // Don't return anything to match the Promise<void> type
  };

  const setCurrencyAndRefresh = (newCurrency: string) => {
    setCurrency(newCurrency);
    if (newCurrency === 'SYP') {
      refreshExchangeRate();
    } else {
      setExchangeRate(1); // Reset to 1 when currency is USD
    }
  };

  // Format price based on current currency and exchange rate
  const formatPrice = (price: number): string => {
    if (currency === 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: 0
      }).format(price);
    } else {
      // Convert to SYP and format
      const priceInSYP = price * exchangeRate;
      return new Intl.NumberFormat('ar-SY', {
        style: 'currency',
        currency: 'SYP',
        maximumFractionDigits: 0
      }).format(priceInSYP);
    }
  };

  return (
    <ExchangeRateContext.Provider value={{
      currency,
      setCurrency: setCurrencyAndRefresh,
      exchangeRate,
      refreshExchangeRate,
      lastUpdated,
      formatPrice
    }}>
      {children}
    </ExchangeRateContext.Provider>
  );
};

export const useExchangeRate = () => useContext(ExchangeRateContext);
