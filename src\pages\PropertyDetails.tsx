
import { useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { PropertyDetails as PropertyDetailsComponent } from "@/components/properties/PropertyDetails";
import { useToast } from "@/components/ui/use-toast";
import { useProperty } from "@/hooks/useProperties";
import { usePropertyNotes, useAddPropertyNote } from "@/hooks/usePropertyNotes";
import { useDeleteProperty } from "@/hooks/useProperties";
import { useTranslation } from "@/contexts/TranslationContext";

const PropertyDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Find the property from Supabase
  const {
    data: property,
    isLoading: isLoadingProperty,
    error: propertyError
  } = useProperty(id || "");

  // Get notes for this property
  const {
    data: notes = [],
    isLoading: isLoadingNotes,
    error: notesError
  } = usePropertyNotes(id || "");

  const addNoteMutation = useAddPropertyNote();
  const deletePropertyMutation = useDeleteProperty();

  const handleAddNote = useCallback((text: string) => {
    if (!id) return;

    addNoteMutation.mutate(
      {
        propertyId: id,
        text,
        createdBy: "Admin User"
      },
      {
        onSuccess: () => {
          toast({
            title: t('propertyDetails.addNote'),
            description: t('properties.deletedSuccess'),
          });
        },
        onError: (error) => {
          toast({
            title: t('common.error'),
            description: `${t('properties.deleteError')}: ${error.message}`,
            variant: "destructive",
          });
        }
      }
    );
  }, [id, addNoteMutation, toast, t]);

  const handleDeleteProperty = useCallback(() => {
    if (!id) return;

    deletePropertyMutation.mutate(id, {
      onSuccess: () => {
        toast({
          title: t('properties.deleted'),
          description: t('properties.deletedSuccess'),
        });

        // Navigate back to properties list
        navigate("/properties");
      },
      onError: (error) => {
        toast({
          title: t('common.error'),
          description: `${t('properties.deleteError')}: ${error.message}`,
          variant: "destructive",
        });
      }
    });
  }, [id, deletePropertyMutation, navigate, toast, t]);

  if (isLoadingProperty || isLoadingNotes) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <p className="text-lg text-muted-foreground">{t('common.loading')}</p>
        </div>
      </Layout>
    );
  }

  if (propertyError || !property) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <h1 className="text-2xl font-bold">{t('propertyDetails.propertyNotFound')}</h1>
          <p className="text-muted-foreground">
            {t('propertyDetails.propertyNotFoundDesc')}
          </p>
          <button
            onClick={() => navigate("/properties")}
            className="mt-4 text-blue-500 hover:underline"
          >
            {t('propertyDetails.backToProperties')}
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <PropertyDetailsComponent
        property={property}
        notes={notes}
        onAddNote={handleAddNote}
        onDelete={handleDeleteProperty}
      />
    </Layout>
  );
};

export default PropertyDetails;
