
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Building, Home, DollarSign, PieChart, TrendingUp, Clock } from "lucide-react";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { properties } from "@/data/mockData";
import { useAuth } from "@/contexts/AuthContext";
import { PropertyCard } from "@/components/properties/PropertyCard";
import { useTranslation } from "@/contexts/TranslationContext";
import { validateBase64Image } from "@/lib/utils";

const Index = () => {
  const { user, isAdmin } = useAuth();
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    totalProperties: 0,
    forSale: 0,
    forRent: 0,
    totalValue: 0,
  });

  // Calculate dashboard stats from mock data
  useEffect(() => {
    const forSaleProps = properties.filter(p => p.status === "for-sale");
    const forRentProps = properties.filter(p => p.status === "for-rent");

    const totalValue = forSaleProps.reduce(
      (sum, property) => sum + property.price,
      0
    );

    setStats({
      totalProperties: properties.length,
      forSale: forSaleProps.length,
      forRent: forRentProps.length,
      totalValue,
    });
  }, []);

  // Get the latest properties
  const recentProperties = properties
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 3);

  // Get the highest valued properties
  const highValueProperties = [...properties]
    .filter(p => p.status === "for-sale")
    .sort((a, b) => b.price - a.price)
    .slice(0, 3);

  return (
    <Layout>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('dashboard.title')}</h1>
            <p className="text-muted-foreground">
              {t('dashboard.welcomeBack')}, {user?.email?.split('@')[0] || t('common.user')}
            </p>
          </div>

          {isAdmin() && (
            <Button asChild>
              <Link to="/properties/add">{t('dashboard.addProperty')}</Link>
            </Button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t('dashboard.totalProperties')}
              </CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalProperties}</div>
              <p className="text-xs text-muted-foreground">
                {stats.forSale} {t('properties.forSale')}, {stats.forRent} {t('properties.forRent')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t('properties.forSale')}
              </CardTitle>
              <Home className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.forSale}</div>
              <p className="text-xs text-muted-foreground">
                {t('dashboard.forSale')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t('properties.forRent')}
              </CardTitle>
              <Home className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.forRent}</div>
              <p className="text-xs text-muted-foreground">
                {t('dashboard.forRent')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t('dashboard.totalValue')}
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: "USD",
                  maximumFractionDigits: 0,
                }).format(stats.totalValue)}
              </div>
              <p className="text-xs text-muted-foreground">
                {t('dashboard.totalValue')}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Recent & High Value Properties */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card className="col-span-1">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <div>
                  <CardTitle>{t('dashboard.recentProperties')}</CardTitle>
                  <CardDescription>{t('dashboard.recentProperties')}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentProperties.map(property => (
                  <div key={property.id} className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-md overflow-hidden flex-shrink-0">
                      <img
                        src={validateBase64Image(property.images[0])}
                        alt={property.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <Link
                        to={`/properties/${property.id}`}
                        className="text-sm font-medium hover:underline line-clamp-1"
                      >
                        {property.title}
                      </Link>
                      <p className="text-xs text-muted-foreground line-clamp-1">
                        {property.location}
                      </p>
                    </div>
                    <div className="text-sm font-semibold">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                        maximumFractionDigits: 0,
                      }).format(property.price)}
                    </div>
                  </div>
                ))}

                <Button variant="outline" asChild className="w-full">
                  <Link to="/properties">{t('dashboard.viewAll')}</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <div>
                  <CardTitle>{t('dashboard.featuredProperties')}</CardTitle>
                  <CardDescription>{t('dashboard.featuredProperties')}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {highValueProperties.map(property => (
                  <div key={property.id} className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-md overflow-hidden flex-shrink-0">
                      <img
                        src={validateBase64Image(property.images[0])}
                        alt={property.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <Link
                        to={`/properties/${property.id}`}
                        className="text-sm font-medium hover:underline line-clamp-1"
                      >
                        {property.title}
                      </Link>
                      <p className="text-xs text-muted-foreground line-clamp-1">
                        {property.location}
                      </p>
                    </div>
                    <div className="text-sm font-semibold">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                        maximumFractionDigits: 0,
                      }).format(property.price)}
                    </div>
                  </div>
                ))}

                <Button variant="outline" asChild className="w-full">
                  <Link to="/properties">{t('dashboard.viewAll')}</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Featured Properties */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <PieChart className="h-5 w-5" />
              <div>
                <CardTitle>{t('dashboard.featuredProperties')}</CardTitle>
                <CardDescription>{t('dashboard.highlightedProperties')}</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {highValueProperties.map(property => (
                <PropertyCard key={property.id} property={property} />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Index;
