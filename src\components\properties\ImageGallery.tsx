
import { useState } from "react";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { cn, validateBase64Image } from "@/lib/utils";
import { Image } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface ImageGalleryProps {
  images: string[];
  className?: string;
}

export function ImageGallery({ images, className }: ImageGalleryProps) {
  const [loadingImages, setLoadingImages] = useState<Record<number, boolean>>({});

  // Ensure images is an array
  const imageArray = Array.isArray(images) ? images : [];

  const handleImageLoad = (index: number) => {
    setLoadingImages((prev) => ({ ...prev, [index]: false }));
  };

  const handleImageError = (index: number) => {
    setLoadingImages((prev) => ({ ...prev, [index]: false }));
  };

  if (!imageArray.length) {
    return (
      <div className={cn("flex items-center justify-center rounded-lg border bg-muted/10 p-8", className)}>
        <div className="flex flex-col items-center gap-2 text-muted-foreground">
          <Image className="h-8 w-8" />
          <p>No images available</p>
        </div>
      </div>
    );
  }

  if (imageArray.length === 1) {
    return (
      <div className={cn("relative aspect-video overflow-hidden rounded-lg", className)}>
        {loadingImages[0] !== false && (
          <Skeleton className="absolute inset-0 z-0 h-full w-full" />
        )}
        <img
          src={validateBase64Image(imageArray[0])}
          alt="Property"
          className="h-full w-full object-cover relative z-10"
          onLoad={() => handleImageLoad(0)}
          onError={() => handleImageError(0)}
        />
      </div>
    );
  }

  return (
    <div className={cn("relative", className)}>
      <Carousel>
        <CarouselContent>
          {imageArray.map((image, index) => (
            <CarouselItem key={index}>
              <div className="aspect-video overflow-hidden rounded-lg relative">
                {loadingImages[index] !== false && (
                  <Skeleton className="absolute inset-0 z-0 h-full w-full" />
                )}
                <img
                  src={validateBase64Image(image)}
                  alt={`Property ${index + 1}`}
                  className="h-full w-full object-cover relative z-10"
                  onLoad={() => handleImageLoad(index)}
                  onError={() => handleImageError(index)}
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="absolute left-2 top-1/2" />
        <CarouselNext className="absolute right-2 top-1/2" />
      </Carousel>
    </div>
  );
}
