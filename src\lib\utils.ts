import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Converts a File object to a Base64 string
 * @param file The file to convert
 * @returns A promise that resolves to the Base64 string
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
}

/**
 * Checks if a string is a Base64 image
 * @param str The string to check
 * @returns True if the string is a Base64 image
 */
export function isBase64Image(str: string): boolean {
  return typeof str === 'string' && str.startsWith('data:image/');
}

/**
 * Ensures a Base64 image string is properly formatted
 * @param str The Base64 image string to validate
 * @returns A properly formatted Base64 image string or a placeholder if invalid
 */
export function validateBase64Image(str: string): string {
  if (!str) return '/placeholder.svg';

  // If it's already a valid Base64 image, return it
  if (isBase64Image(str)) return str;

  // If it's a URL (not Base64), return it as is
  if (str.startsWith('http') || str.startsWith('/')) return str;

  // If it's a Base64 string without the data URL prefix, add it
  // Try to detect the image format based on the first few characters of the Base64 string
  if (typeof str === 'string' && str.length > 10) {
    // Check if it's likely a raw Base64 string
    const isLikelyBase64 = /^[A-Za-z0-9+/]+=*$/.test(str.substring(0, 20));

    if (isLikelyBase64) {
      // Default to JPEG if we can't determine the type
      return `data:image/jpeg;base64,${str}`;
    }
  }

  // If we can't determine what it is, return a placeholder
  return '/placeholder.svg';
}
