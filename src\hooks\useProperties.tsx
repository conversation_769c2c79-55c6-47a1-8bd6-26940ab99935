
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase, getCurrentUserId } from '@/lib/supabase';
import { Property } from '@/types/property';
import { isBase64Image } from '@/lib/utils';

// Transform the database property format to our application format
const transformProperty = (dbProperty: any): Property => {
  return {
    id: dbProperty.id,
    title: dbProperty.title,
    description: dbProperty.description,
    price: dbProperty.price,
    location: dbProperty.location,
    bedrooms: dbProperty.bedrooms,
    bathrooms: dbProperty.bathrooms,
    floor: dbProperty.floor,
    size: dbProperty.size,
    furnished: dbProperty.furnished,
    type: dbProperty.type,
    status: dbProperty.status,
    images: dbProperty.images || [],
    createdAt: dbProperty.created_at,
    updatedAt: dbProperty.updated_at,
    created_by: dbProperty.created_by
  };
};

// Hook to fetch all properties
export const useProperties = () => {
  return useQuery({
    queryKey: ['properties'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(transformProperty);
    }
  });
};

// Hook to fetch a single property
export const useProperty = (id: string) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return transformProperty(data);
    },
    enabled: !!id
  });
};

// Hook to create a property
export const useCreateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (property: Omit<Property, 'id' | 'createdAt' | 'updatedAt' | 'created_by'>) => {
      try {
        // Get current user ID
        const userId = await getCurrentUserId();
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log('Current user ID:', userId);

        // No need to process images as they are already Base64 strings
        const processedImages = property.images;

        // Make sure the DB schema fields match exactly what Supabase expects
        const propertyToInsert = {
          title: property.title,
          description: property.description,
          price: property.price,
          location: property.location,
          bedrooms: property.bedrooms,
          bathrooms: property.bathrooms,
          floor: property.floor,
          size: property.size,
          furnished: property.furnished,
          type: property.type,
          status: property.status,
          images: processedImages,
          created_by: userId,  // This must match EXACTLY your Supabase column name
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        console.log('Inserting property with created_by:', JSON.stringify(propertyToInsert, null, 2));

        // Check the session is valid before inserting
        const sessionResponse = await supabase.auth.getSession();
        if (!sessionResponse.data.session) {
          throw new Error('No active session found. Please log in again.');
        }

        const { data, error } = await supabase
          .from('properties')
          .insert(propertyToInsert)
          .select()
          .single();

        if (error) {
          console.error('Supabase error details:', error);
          throw new Error(`Database error: ${error.message} (Code: ${error.code})`);
        }

        if (!data) {
          throw new Error('No data returned from insert operation');
        }

        return transformProperty(data);
      } catch (error) {
        console.error('Error creating property:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    }
  });
};

// Hook to update a property
export const useUpdateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...property }: Partial<Property> & { id: string }) => {
      try {
        // Get current user ID
        const userId = await getCurrentUserId();
        if (!userId) {
          throw new Error('User not authenticated');
        }

        // No need to process images as they are already Base64 strings
        const processedImages = property.images;

        const propertyToUpdate = {
          title: property.title,
          description: property.description,
          price: property.price,
          location: property.location,
          bedrooms: property.bedrooms,
          bathrooms: property.bathrooms,
          floor: property.floor,
          size: property.size,
          furnished: property.furnished,
          type: property.type,
          status: property.status,
          images: processedImages,
          updated_at: new Date().toISOString()
          // Note: We don't update user_id to keep the original owner
        };

        console.log('Updating property:', id, JSON.stringify(propertyToUpdate, null, 2));

        const { data, error } = await supabase
          .from('properties')
          .update(propertyToUpdate)
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error('Supabase update error:', error);
          throw new Error(`Database error: ${error.message} (Code: ${error.code})`);
        }

        return transformProperty(data);
      } catch (error) {
        console.error('Error updating property:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['property', data.id] });
    }
  });
};

// Hook to delete a property
export const useDeleteProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const { error } = await supabase
          .from('properties')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Supabase delete error:', error);
          throw new Error(`Database error: ${error.message} (Code: ${error.code})`);
        }

        return id;
      } catch (error) {
        console.error('Error deleting property:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    }
  });
};
