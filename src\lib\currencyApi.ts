const API_KEY = process.env.VITE_CURRENCY_API_KEY ?? '1ad1a3ab6c554c719bee70d174286169'; // fallback

// Base URL only
const BASE_URL = 'https://api.exchangerate.host/latest?base=USD&symbols=SYP';

/**
 * Fetches the current exchange rate from USD to SYP
 * @returns Promise with the exchange rate value
 */
export const fetchUsdToSypRate = async (): Promise<number> => {
  try {
    if (!API_KEY) {
      console.error('Currency API key is missing. Please set VITE_CURRENCY_API_KEY in your .env file.');
      throw new Error('Currency API key is missing');
    }

    const url = `${BASE_URL}?apikey=${API_KEY}&symbols=SYP&base=USD`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.rates && data.rates.SYP) {
      return parseFloat(data.rates.SYP);
    } else {
      throw new Error('Failed to get SYP rate from API response');
    }
  } catch (error) {
    console.error('Error fetching exchange rate:', error);
    throw error;
  }
};
