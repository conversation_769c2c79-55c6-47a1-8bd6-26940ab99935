
import { <PERSON> } from "react-router-dom";
import { Eye, Edit, Home, MapPin, Hotel, Bath } from "lucide-react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>Footer,
  CardHeader,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Property } from "@/types/property";
import { useAuth } from "@/contexts/AuthContext";
import { useTranslation } from "@/contexts/TranslationContext";
import { validateBase64Image } from "@/lib/utils";
import { useExchangeRate } from "@/contexts/ExchangeRateContext";

interface PropertyCardProps {
  property: Property;
}

export function PropertyCard({ property }: PropertyCardProps) {
  const { isAdmin } = useAuth();
  const { t } = useTranslation();
  const { formatPrice } = useExchangeRate();
  const {
    id, title, location, price,
    bedrooms, bathrooms, size, status, type
  } = property;

  return (
    <Card className="overflow-hidden">
      <div className="relative aspect-video w-full overflow-hidden">
        <img
          src={validateBase64Image(property.images[0])}
          alt={title}
          className="h-full w-full object-cover transition-all hover:scale-105"
        />
        <div className="absolute right-2 top-2">
          <Badge variant={status === "for-sale" ? "secondary" : "default"}>
            {status === "for-sale" ? t('properties.forSale') : t('properties.forRent')}
          </Badge>
        </div>
      </div>

      <CardHeader className="p-4 pb-0">
        <div className="flex justify-between">
          <div>
            <h3 className="font-semibold line-clamp-1">{title}</h3>
            <div className="mt-1 flex items-center text-sm text-muted-foreground">
              <MapPin className="mr-1 h-3 w-3" /> {location}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-4">
        <div className="text-lg font-bold">
          {formatPrice(price)}
          {status === "for-rent" && <span className="text-sm font-normal">/{t('properties.month')}</span>}
        </div>

        <div className="mt-4 flex items-center justify-between text-sm">
          {type !== "land" && (
            <>
              <div className="flex items-center gap-1">
                <Hotel className="h-4 w-4" />
                <span>{bedrooms} {bedrooms === 1 ? t('properties.bed') : t('properties.beds')}</span>
              </div>

              <div className="flex items-center gap-1">
                <Bath className="h-4 w-4" />
                <span>{bathrooms} {bathrooms === 1 ? t('properties.bath') : t('properties.baths')}</span>
              </div>
            </>
          )}

          <div className="flex items-center gap-1">
            <Home className="h-4 w-4" />
            <span>{size} {t('properties.size')}</span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex gap-2">
        <Button asChild variant="outline" size="sm" className="flex-1">
          <Link to={`/properties/${id}`}>
            <Eye className="mr-2 h-4 w-4" /> {t('common.view')}
          </Link>
        </Button>

        {isAdmin() && (
          <Button asChild variant="outline" size="sm" className="flex-1">
            <Link to={`/properties/${id}/edit`}>
              <Edit className="mr-2 h-4 w-4" /> {t('common.edit')}
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
