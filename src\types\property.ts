
export interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  location: string;
  bedrooms: number;
  bathrooms: number;
  floor: number | null;
  size: number;
  furnished: "furnished" | "semi-furnished" | "unfurnished";
  type: "apartment" | "house" | "villa" | "land" | "commercial" | "other";
  status: "for-sale" | "for-rent";
  images: string[];
  createdAt: string;
  updatedAt: string;
  created_by: string; // Changed from user_id to created_by to match the database
}

// Add these types to fix the TypeScript errors
export type PropertyType = "apartment" | "house" | "villa" | "land" | "commercial" | "other";
export type PropertyStatus = "for-sale" | "for-rent";
export type FurnishedStatus = "furnished" | "semi-furnished" | "unfurnished";

export interface PropertyNote {
  id: string;
  property_id: string;
  text: string; // Changed content to text to match the database
  created_at: string;
  created_by: string; // Changed user_id to created_by to match the database
}
