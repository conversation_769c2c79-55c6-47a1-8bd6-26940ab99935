
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { PropertyNote } from '@/types/property';

// Transform the database note format to our application format
const transformNote = (dbNote: any): PropertyNote => {
  return {
    id: dbNote.id,
    property_id: dbNote.property_id,
    text: dbNote.text,
    created_by: dbNote.created_by,
    created_at: dbNote.created_at
  };
};

// Hook to fetch notes for a property
export const usePropertyNotes = (propertyId: string) => {
  return useQuery({
    queryKey: ['propertyNotes', propertyId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('notes')
        .select('*')
        .eq('property_id', propertyId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      return data.map(transformNote);
    },
    enabled: !!propertyId
  });
};

// Hook to add a note
export const useAddPropertyNote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ propertyId, text, createdBy }: { propertyId: string; text: string; createdBy: string }) => {
      const noteToInsert = {
        property_id: propertyId,
        text,
        created_by: createdBy,
        created_at: new Date().toISOString()
      };
      
      const { data, error } = await supabase
        .from('notes')
        .insert(noteToInsert)
        .select()
        .single();
      
      if (error) throw error;
      
      return transformNote(data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['propertyNotes', data.property_id] });
    }
  });
};
