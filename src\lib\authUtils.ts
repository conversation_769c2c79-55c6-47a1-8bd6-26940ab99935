import { supabase } from './supabase';
import { toast } from '@/components/ui/sonner';

/**
 * Handles authentication errors, particularly refresh token issues
 * @param error The error object to handle
 * @param redirectToLogin Optional callback to redirect to login page
 * @returns <PERSON>olean indicating if the error was handled
 */
export const handleAuthError = (error: any, redirectToLogin?: () => void): boolean => {
  if (!error) return false;
  
  const errorMessage = error.message || error.toString();
  
  // Handle refresh token errors
  if (errorMessage.includes('Invalid Refresh Token') || 
      errorMessage.includes('Refresh Token Not Found')) {
    console.warn('Auth token error detected:', errorMessage);
    
    // Clear any stored tokens
    localStorage.removeItem('supabase.auth.token');
    
    // Show toast notification
    toast.error('Session expired', {
      description: 'Your session has expired. Please sign in again.',
    });
    
    // Sign out from Supabase to clear any remaining session data
    supabase.auth.signOut().catch(e => {
      console.error('Error signing out after token error:', e);
    });
    
    // Redirect to login if callback provided
    if (redirectToLogin) {
      setTimeout(redirectToLogin, 1000);
    }
    
    return true;
  }
  
  return false;
};

/**
 * Refreshes the authentication session
 * @returns Promise resolving to a boolean indicating success
 */
export const refreshSession = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('Error refreshing session:', error);
      handleAuthError(error);
      return false;
    }
    
    return !!data.session;
  } catch (error) {
    console.error('Exception refreshing session:', error);
    handleAuthError(error);
    return false;
  }
};

/**
 * Clears all authentication data and redirects to login
 * @param redirectToLogin Optional callback to redirect to login page
 */
export const clearAuthAndRedirect = (redirectToLogin?: () => void) => {
  // Clear any stored tokens
  localStorage.removeItem('supabase.auth.token');
  
  // Sign out from Supabase
  supabase.auth.signOut().catch(e => {
    console.error('Error signing out during clear auth:', e);
  });
  
  // Redirect to login if callback provided
  if (redirectToLogin) {
    setTimeout(redirectToLogin, 500);
  }
};
