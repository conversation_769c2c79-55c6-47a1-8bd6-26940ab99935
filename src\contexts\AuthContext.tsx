
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { supabase, getCurrentUser } from "@/lib/supabase";
import { Session, User } from "@supabase/supabase-js";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";

interface AuthContextType {
  session: Session | null;
  user: User | null;
  signIn: (email: string, password: string) => Promise<{ error: any | null }>;
  signUp: (email: string, password: string) => Promise<{ error: any | null }>;
  signOut: () => Promise<void>;
  loading: boolean;
  isAdmin: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // Check for active session
    const initializeAuth = async () => {
      setLoading(true);

      try {
        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          if (sessionError.message.includes('Invalid Refresh Token')) {
            console.warn('Invalid refresh token detected, clearing auth state');
            // Clear local storage to remove any invalid tokens
            localStorage.removeItem('supabase.auth.token');
            setSession(null);
            setUser(null);
          } else {
            console.error('Session error:', sessionError);
          }
        } else {
          setSession(session);
          setUser(session?.user ?? null);
        }

        // Set up auth state listener
        const { data } = supabase.auth.onAuthStateChange(async (event, session) => {
          console.log("Auth state changed:", event);

          if (event === 'TOKEN_REFRESHED') {
            console.log('Token refreshed successfully');
          } else if (event === 'SIGNED_OUT') {
            // Clear any stored tokens on sign out
            localStorage.removeItem('supabase.auth.token');
          }

          setSession(session);
          setUser(session?.user ?? null);
          setLoading(false);
        });

        setLoading(false);

        // Cleanup subscription
        return () => {
          data.subscription.unsubscribe();
        };
      } catch (error) {
        console.error("Error initializing auth:", error);
        // If there's an error with authentication, clear the state
        setSession(null);
        setUser(null);
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      // Clear any existing tokens before signing in to prevent conflicts
      localStorage.removeItem('supabase.auth.token');

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Handle specific auth errors
        if (error.message.includes('Invalid login credentials')) {
          toast({
            title: "Sign in failed",
            description: "Invalid email or password. Please try again.",
            variant: "destructive",
          });
        } else if (error.message.includes('Invalid Refresh Token')) {
          toast({
            title: "Session expired",
            description: "Your session has expired. Please sign in again.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Sign in failed",
            description: error.message,
            variant: "destructive",
          });
        }
        return { error };
      }

      toast({
        title: "Signed in successfully",
        description: `Welcome back, ${email}!`,
      });

      return { error: null };
    } catch (error) {
      console.error("Error signing in:", error);
      toast({
        title: "Sign in failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      return { error };
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        toast({
          title: "Sign up failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      toast({
        title: "Account created",
        description: "Please check your email to verify your account.",
      });

      return { error: null };
    } catch (error) {
      console.error("Error signing up:", error);
      return { error };
    }
  };

  const signOut = async () => {
    try {
      // First clear any stored tokens
      localStorage.removeItem('supabase.auth.token');

      // Then sign out from Supabase
      await supabase.auth.signOut();

      // Clear session and user state
      setSession(null);
      setUser(null);

      toast({
        title: "Signed out",
        description: "You have been signed out successfully.",
      });
    } catch (error) {
      console.error("Error signing out:", error);

      // Even if there's an error, clear the local state
      localStorage.removeItem('supabase.auth.token');
      setSession(null);
      setUser(null);

      toast({
        title: "Sign out issues",
        description: "There were some issues during sign out, but you've been signed out successfully.",
        variant: "default",
      });
    }
  };

  // Admin check based on email domain or other criteria
  const isAdmin = () => {
    // You can implement more sophisticated admin checks
    return !!user;
  };

  return (
    <AuthContext.Provider value={{ session, user, signIn, signUp, signOut, loading, isAdmin }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
