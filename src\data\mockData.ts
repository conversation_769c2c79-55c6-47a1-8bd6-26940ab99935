
import { Property, PropertyNote } from "@/types/property";

// This is just sample data for development
// In production, we'll use data from Supabase
export const properties: Property[] = [
  {
    id: "1",
    title: "Luxury Apartment in Downtown",
    description: "A beautiful apartment with great views of the city skyline.",
    price: 750000,
    location: "Downtown, City Center",
    bedrooms: 3,
    bathrooms: 2,
    floor: 15,
    size: 120, // Changed from 'area' to 'size'
    furnished: "furnished",
    type: "apartment",
    status: "for-sale",
    images: ["/placeholder.svg"],
    createdAt: "2023-01-15T12:00:00Z",
    updatedAt: "2023-02-10T09:30:00Z",
    created_by: "admin-user", // Added missing created_by field
  },
  {
    id: "2",
    title: "Family House with Garden",
    description: "Spacious family house with a beautiful garden in a quiet neighborhood.",
    price: 950000,
    location: "Suburbia, Greenfield",
    bedrooms: 4,
    bathrooms: 3,
    floor: 0,
    size: 220, // Changed from 'area' to 'size'
    furnished: "semi-furnished",
    type: "house",
    status: "for-sale",
    images: ["/placeholder.svg"],
    createdAt: "2023-03-05T14:20:00Z",
    updatedAt: "2023-03-25T11:15:00Z",
    created_by: "admin-user", // Added missing created_by field
  },
  {
    id: "3",
    title: "Modern Villa with Pool",
    description: "Luxurious villa with private pool and panoramic sea views.",
    price: 1850000,
    location: "Seaside, Ocean Drive",
    bedrooms: 5,
    bathrooms: 4,
    floor: 0,
    size: 350, // Changed from 'area' to 'size'
    furnished: "furnished",
    type: "villa",
    status: "for-sale",
    images: ["/placeholder.svg"],
    createdAt: "2023-04-12T09:45:00Z",
    updatedAt: "2023-05-01T16:30:00Z",
    created_by: "admin-user", // Added missing created_by field
  },
  {
    id: "4",
    title: "Commercial Space in Business Center",
    description: "Prime commercial space in the heart of the business district.",
    price: 12000,
    location: "Business District, Commerce Ave",
    bedrooms: 0,
    bathrooms: 2,
    floor: 3,
    size: 200, // Changed from 'area' to 'size'
    furnished: "unfurnished",
    type: "commercial",
    status: "for-rent",
    images: ["/placeholder.svg"],
    createdAt: "2023-05-20T10:10:00Z",
    updatedAt: "2023-06-15T13:25:00Z",
    created_by: "admin-user", // Added missing created_by field
  },
  {
    id: "5",
    title: "Development Land with Planning Permission",
    description: "Large plot of land with planning permission for residential development.",
    price: 500000,
    location: "Outskirts, Development Zone",
    bedrooms: 0,
    bathrooms: 0,
    floor: null,
    size: 1500, // Changed from 'area' to 'size'
    furnished: "unfurnished",
    type: "land",
    status: "for-sale",
    images: ["/placeholder.svg"],
    createdAt: "2023-07-01T08:00:00Z",
    updatedAt: "2023-07-10T14:45:00Z",
    created_by: "admin-user", // Added missing created_by field
  },
];

// Sample property notes
export const propertyNotes: PropertyNote[] = [
  {
    id: "1",
    property_id: "1", // Changed from propertyId to property_id
    text: "Client viewed on 15th March. Interested but concerned about the price.",
    created_by: "Jane Smith", // Changed from createdBy to created_by
    created_at: "2023-03-15T14:30:00Z", // Changed from createdAt to created_at
  },
  {
    id: "2",
    property_id: "1", // Changed from propertyId to property_id
    text: "Follow-up call scheduled for 22nd March.",
    created_by: "John Doe", // Changed from createdBy to created_by
    created_at: "2023-03-16T09:15:00Z", // Changed from createdAt to created_at
  },
  {
    id: "3",
    property_id: "2", // Changed from propertyId to property_id
    text: "Needs minor repairs in the bathroom before showing to clients.",
    created_by: "Jane Smith", // Changed from createdBy to created_by
    created_at: "2023-03-18T11:00:00Z", // Changed from createdAt to created_at
  }
];
